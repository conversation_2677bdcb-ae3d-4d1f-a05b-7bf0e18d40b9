
import httpx

def fetch_url(url: str) -> str:
    """
    Fetch content from a URL using httpx.

    Args:
        url (str): The URL to fetch

    Returns:
        str: The response content as text
    """
    try:
        with httpx.Client() as client:
            response = client.get(url)
            response.raise_for_status()  # Raises an exception for bad status codes
            return response.text
    except httpx.RequestError as e:
        print(f"An error occurred while requesting {url}: {e}")
        return ""
    except httpx.HTTPStatusError as e:
        print(f"Error response {e.response.status_code} while requesting {url}")
        return ""

def main():
    # Example usage
    url = "https://httpbin.org/get"  # Test URL that returns JSON
    print(f"Fetching content from: {url}")

    content = fetch_url(url)
    if content:
        print("Response received:")
        print(content[:500])  # Print first 500 characters
    else:
        print("Failed to fetch content")

if __name__ == "__main__":
    main()
